import axiosInstance from '@/lib/axios';

// Reuse the same interfaces from engagement survey for consistency
export interface SurveyQuestion {
  id: string;
  title: string;
  label: string;
  text?: string; // Add text property for instruction content
  type: string;
  resourcetype: string;
  section: number;
  mandatory: boolean;
  collect_feedback: boolean;
  is_reverse_scale: boolean;
  options?: any;
  response?: {
    id: string;
    value: any;
    is_valid: boolean;
  };
}

export interface SurveySection {
  id: number;
  title: string;
  questions: SurveyQuestion[];
}

export interface SurveyMeta {
  title: string;
  endDate: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  hideNoBasisOption?: boolean;
}

export interface ExitSurveyData {
  meta: SurveyMeta;
  sections: SurveySection[];
  questions: SurveyQuestion[];
  responseId: string;
  versionId: string;
  demographicsId?: string;
  completion: number;
}

/**
 * Get survey version ID from survey index ID
 */
const getSurveyVersionId = async (surveyIndexId: string) => {
  const response = await axiosInstance.get(`/survey/survey-index/${surveyIndexId}/`);
  const data = response.data;

  if (data.surveys && data.surveys.length > 0) {
    const [version] = data.surveys;
    return {
      versionId: version.id,
      demographicsId: data.demographic_id,
    };
  }

  throw new Error('No survey version found');
};

/**
 * Transform raw survey data into structured format (same as engagement survey)
 */
export const transformSurveyData = (_surveyData: any, responsesData: any, responseId: string, versionId: string, demographicsId?: string): ExitSurveyData => {
  // The responsesData comes from /survey/survey-response/{id} and contains survey_detail with sections and components
  const surveyDetail = responsesData.survey_detail || {};
  const { sections } = surveyDetail;

  // Create a map of responses by question ID from question_responses
  const responseMap = new Map();
  if (responsesData.question_responses) {
    responsesData.question_responses.forEach((response: any) => {
      responseMap.set(response.question, response);
    });
  }

  // Transform sections and their components (questions)
  const transformedSections: SurveySection[] = (sections || []).map((section: any) => {
    const sectionQuestions: SurveyQuestion[] = (section.components || [])
      .filter((component: any) => component.resourcetype !== "QuestionNumber") // Filter out number questions
      .map((component: any) => {
        const response = responseMap.get(component.id);

        return {
          id: component.id,
          title: component.label,
          label: component.label,
          text: component.text, // Add text property for instruction content
          type: component.resourcetype,
          resourcetype: component.resourcetype,
          section: section.id,
          mandatory: component.mandatory,
          collect_feedback: component.collect_feedback,
          is_reverse_scale: component.is_reverse_scale,
          options: component.options || {},
          response: response ? {
            id: response.id,
            value: response.value,
            is_valid: response.is_valid,
          } : undefined,
        };
      });

    return {
      id: section.id,
      title: section.name,
      questions: sectionQuestions,
    };
  });

  // Flatten all questions for overall stats
  const allQuestions = transformedSections.flatMap(section => section.questions);

  // Calculate completion percentage
  const totalQuestions = allQuestions.length;
  const answeredQuestions = allQuestions.filter(q =>
    q.response?.value !== undefined &&
    q.response?.value !== null &&
    q.response?.value !== ''
  ).length;
  const completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

  // Get survey metadata from responsesData
  const surveyMeta = responsesData.index || {};

  return {
    meta: {
      title: surveyMeta.title || 'Exit Survey',
      endDate: surveyMeta.deadline || '',
      canSubmit: completion === 100,
      nextTitle: 'Next',
      buttonType: 'primary',
      hideBack: false,
      hideNoBasisOption: surveyDetail.hide_no_basis_option || false,
    },
    sections: transformedSections,
    questions: allQuestions,
    responseId,
    versionId,
    demographicsId,
    completion,
  };
};

/**
 * Get exit survey data by creating a response and fetching sections
 */
export const getExitSurveyData = async (surveyIndexId: string): Promise<ExitSurveyData> => {
  try {
    // Step 1: Get survey version ID from survey index ID
    const { versionId, demographicsId } = await getSurveyVersionId(surveyIndexId);

    if (!versionId) {
      throw new Error('No Survey Version Present');
    }

    // Step 2: Create survey response with version ID
    const responsePayload = {
      survey: versionId,
      resourcetype: 'SurveyResponseExit'
    };

    const responseResult = await axiosInstance.post('/survey/survey-response/', responsePayload);
    const responseId = responseResult.data.id;

    // Step 3: Get survey structure and existing responses in parallel
    const [surveyStructure, existingResponses] = await Promise.all([
      axiosInstance.get(`/survey/survey/${versionId}?survey=${versionId}`),
      axiosInstance.get(`/survey/survey-response/${responseId}`)
    ]);

    // Step 4: Transform and return structured data
    return transformSurveyData(
      surveyStructure.data,
      existingResponses.data,
      responseId,
      versionId,
      demographicsId
    );
  } catch (error) {
    console.error('Error fetching exit survey data:', error);
    throw new Error('Failed to load exit survey data');
  }
};

/**
 * Save question response
 */
export const saveQuestionResponse = async (
  responseId: string,
  questionId: string,
  value: any,
  existingResponseId?: string
): Promise<void> => {
  try {
    const payload = {
      survey_response: responseId,
      question: questionId,
      value: value
    };

    if (existingResponseId) {
      await axiosInstance.patch(`/survey/question-response/${existingResponseId}/`, payload);
    } else {
      await axiosInstance.post('/survey/question-response/', payload);
    }
  } catch (error) {
    console.error('Error saving question response:', error);
    throw error;
  }
};

/**
 * Save comment response
 */
export const saveCommentResponse = async (
  responseId: string,
  questionId: string,
  comment: string,
  commentId?: string
): Promise<void> => {
  try {
    const payload = {
      survey_response: responseId,
      question: questionId,
      value: comment
    };

    if (commentId) {
      await axiosInstance.patch(`/survey/comment-response/${commentId}/`, payload);
    } else {
      await axiosInstance.post('/survey/comment-response/', payload);
    }
  } catch (error) {
    console.error('Error saving comment response:', error);
    throw error;
  }
};

/**
 * Submit exit survey
 */
export const submitExitSurvey = async (responseId: string): Promise<void> => {
  try {
    await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
  } catch (error) {
    console.error('Error submitting exit survey:', error);
    throw error;
  }
};

// Export the submit function with the same name as other surveys for consistency
export const submitSurvey = submitExitSurvey;
