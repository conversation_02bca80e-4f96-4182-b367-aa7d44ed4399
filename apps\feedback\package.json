{"name": "feedback", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.7.0", "@repo/ui": "workspace:*", "@types/lodash": "^4.17.16", "@types/luxon": "^3.6.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.488.0", "luxon": "^3.6.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-redux": "^9.2.0", "react-router": "^7.5.1", "react-use": "^17.6.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "3", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}