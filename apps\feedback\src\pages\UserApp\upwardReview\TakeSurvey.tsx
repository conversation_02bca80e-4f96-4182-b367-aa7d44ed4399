import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { USER_ROUTES } from '@/app.routes';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';
import SubmitConfirmation from './components/SubmitConfirmation';
import SurveySidebar from './components/SurveySidebar';
import { useSurveyData } from './hooks/useSurveyData';

const TakeSurvey: React.FC = () => {
  const { id, surveyId } = useParams<{ id: string; surveyId: string }>();
  const navigate = useNavigate();

  // Use custom hook for survey data management
  const {
    isLoading,
    isSaving,
    error,
    meta,
    sections,
    currentSection,
    questions,
    allQuestions,
    analytics,
    faqs,
    fetchSurveyData,
    loadQuestionsForSection,
    updateQuestionResponse,
    submitSurvey,
    setCurrentSection,
    getCompletionPercentage
  } = useSurveyData();

  // Local state for UI
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [showFaqDialog, setShowFaqDialog] = useState(false);

  // Initialize survey data on component mount
  useEffect(() => {
    if (id && surveyId) {
      fetchSurveyData(id, surveyId);
    }
  }, [id, surveyId, fetchSurveyData]);

  // Handle section selection
  const handleSectionSelect = async (section: any) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
    await loadQuestionsForSection(section.id);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle navigation
  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBack = () => {
    navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
  };

  // Handle survey submission
  const handleSubmit = async () => {
    if (!id) return;

    try {
      await submitSurvey(id);
      navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
    } catch (err) {
      // Error is already handled in the hook
    }
  };

  // Handle question updates using the hook
  const handleQuestionUpdate = async (questionId: string, response: any, feedback?: string) => {
    await updateQuestionResponse(questionId, response, feedback);
  };

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Submit confirmation state
  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        meta={meta}
        analytics={analytics}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={handleSubmit}
        isLoading={isSaving}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 w-full">
      <div className="flex w-full">
        {/* Sidebar */}
        <SurveySidebar
          meta={meta}
          sections={sections}
          currentSection={currentSection}
          analytics={analytics}
          faqs={faqs}
          isLoading={isLoading}
          showFaqDialog={showFaqDialog}
          allQuestions={allQuestions}
          onSectionSelect={handleSectionSelect}
          onBack={handleBack}
          onFaqDialogChange={setShowFaqDialog}
        />

        {/* Main Content */}
        <div className="flex-1 bg-white dark:bg-gray-800 min-h-screen">
          {isLoading ? (
            <div className="p-6 space-y-6">
              <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-3/4"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
            </div>
          ) : !currentSection ? (
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-400">No sections available</p>
            </div>
          ) : (
            <>
              {/* Section Header - Light blue bar like legacy */}
              <div className="bg-blue-200 dark:bg-blue-800 px-6 py-4 border-b dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-medium text-gray-800 dark:text-white">{currentSection.title}</h2>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                    <span>{getCompletionPercentage()}% Completed</span>
                    <span>•</span>
                    <span>
                      {isSaving ? 'Saving...' : `Saved ${meta?.lastModified || 'Just now'}`}
                    </span>
                  </div>
                </div>
              </div>

              {/* Questions Content */}
              <div className="p-6">
                <div className="space-y-8">
                  {questions.length === 0 ? (
                    <p className="text-gray-600 dark:text-gray-400">No questions in this section</p>
                  ) : (
                    questions.map((question, index) => (
                      <QuestionRenderer
                        key={question.id}
                        question={question}
                        questionNumber={index + 1}
                        onResponseUpdate={(questionId: string, value: any) =>
                          handleQuestionUpdate(questionId, value)
                        }
                        onCommentUpdate={(questionId: string, comment: string) =>
                          handleQuestionUpdate(questionId, undefined, comment)
                        }
                      />
                    ))
                  )}
                </div>

                {/* Bottom section with remaining questions */}
                {currentSection && questions.length > 0 && (
                  <div className="mt-12 text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      {questions.filter(q =>
                        !q.response || q.response.value === undefined || q.response.value === null || q.response.value === ''
                      ).length} Questions left in the section
                    </p>
                    <Button
                      onClick={handleNext}
                      className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 px-8"
                    >
                      {sections && currentSection &&
                       sections.findIndex(s => s.id === currentSection.id) === sections.length - 1
                        ? 'Submit my response'
                        : 'Next'
                      }
                    </Button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default TakeSurvey;
