const AUTH = "/auth";
// const USER = "/user";

// type ID = number | string;

const getLauncherUrls = () => {
  const ADMIN = "/launcher/admin";

  return {
    default: "/launcher",
    home: `/launcher/home`,
    admin: {
      default: ADMIN,
      employees: {
        default: `${ADMIN}/employees`,

        // All employees children routes go in this.
      },
      files: {
        default: `${ADMIN}/files`,
        viewFile: (id: string) => `${ADMIN}/file/${id}`
        // All files children routes go in this.
      },
      pairings: {
        default: `${ADMIN}/pairings`,
        viewPairing: (id: string) => `${ADMIN}/pairing/${id}`
        // All files children routes go in this.
      },
      admins: {
        default: `${ADMIN}/admins`,
      },
      rolesPermissions: {
        default: `${ADMIN}/roles-permissions`,
      },
      orgSettings: {
        default: `${ADMIN}/org-settings`,
      },
      textTemplates: {
        default: `${ADMIN}/templates`,
      },
    },
  };
};

const appUrls = {
  site: {
    default: "https://www.unmatched.io/",
  },
  faq: "/faq",
  contactUs: "/contact",
  privacy: "/privacy",
  terms: "/terms",
  confidentiality: "/confidentiality",
  auth: {
    // Authentication
    default: AUTH,
    // accountInformation: `${AUTH}/account-information`,
    login: `${AUTH}/login`,
    requestPassword: `${AUTH}/request-password`,
    getMagicLinkUrl: (email = "", token = "") =>
      `${AUTH}/magiclink/${email}/${token}`,
    terms: `${AUTH}/terms`,
    confidentiality: `${AUTH}/confidentiality`,
    privacyPolicy: `${AUTH}/privacy-policy`,
    // Dynamic Urls
    getActivationUrl: (email: string, token: string) =>
      `${AUTH}/activation/${email}/${token}`,
    getResetPasswordUrl: (email: string, token: string) =>
      `${AUTH}/reset-password/${email}/${token}`,
  },
  oidcCallback: '/oidc/callback',
  // launcher: "/launcher",
  launcher: getLauncherUrls(),
  logout: "/logout",
};

export default appUrls;
