import React from 'react';
import { Button } from '@repo/ui/components/button';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';
import { type ExitSurveyData, type SurveySection } from '@/services/exitSurveyService';

interface SurveyContentProps {
  isLoading: boolean;
  isSaving: boolean;
  surveyData: ExitSurveyData | null;
  currentSection: SurveySection | null;
  lastSaved: Date;
  onResponseUpdate: (questionId: string, value: any, responseId?: string) => void;
  onCommentUpdate: (questionId: string, comment: string, commentId?: string) => void;
  onNext: () => void;
}

const SurveyContent: React.FC<SurveyContentProps> = ({
  isLoading,
  isSaving,
  surveyData,
  currentSection,
  lastSaved,
  onResponseUpdate,
  onCommentUpdate,
  onNext
}) => {
  if (isLoading) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 min-h-screen">
        <div className="p-6 space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-3/4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
        </div>
      </div>
    );
  }

  if (!surveyData || !currentSection) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 min-h-screen flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">No survey data available</p>
      </div>
    );
  }

  const isLastSection = surveyData.sections.findIndex(s => s.id === currentSection.id) === surveyData.sections.length - 1;

  return (
    <div className="flex-1 bg-white dark:bg-gray-800 min-h-screen">
      {/* Section Header - Red bar for exit survey */}
      <div className="bg-red-100 dark:bg-red-900/20 px-6 py-4 border-b dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-800 dark:text-white">{currentSection.title}</h2>
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
            <span>{surveyData.completion}% Completed</span>
            <span>•</span>
            <span>
              {isSaving ? 'Saving...' : `Saved ${lastSaved.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}`}
            </span>
          </div>
        </div>
      </div>

      {/* Questions Content */}
      <div className="p-6">
        <div className="space-y-8">
          {currentSection.questions.length === 0 ? (
            <p className="text-gray-600 dark:text-gray-400">No questions in this section</p>
          ) : (
            currentSection.questions.map((question, index) => (
              <QuestionRenderer
                key={question.id}
                question={question}
                questionNumber={index + 1}
                onResponseUpdate={onResponseUpdate}
                onCommentUpdate={onCommentUpdate}
              />
            ))
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-end mt-8 pt-6 border-t dark:border-gray-700">
          <Button 
            onClick={onNext}
            className="px-8"
          >
            {isLastSection ? 'Submit Survey' : 'Next Section'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SurveyContent;
