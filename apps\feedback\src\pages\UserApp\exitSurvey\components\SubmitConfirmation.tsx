import React from 'react';
import { But<PERSON> } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';

interface SubmitConfirmationProps {
  isSubmitting: boolean;
  onBack: () => void;
  onSubmit: () => void;
}

const SubmitConfirmation: React.FC<SubmitConfirmationProps> = ({
  isSubmitting,
  onBack,
  onSubmit
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Submit Exit Survey</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Thank you for taking the time to complete this exit survey. Your feedback is valuable to us and will help improve our organization.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Once submitted, you will not be able to make changes to your responses.
            </p>
          </div>
          
          <div className="flex gap-3">
            <Button 
              variant="outline" 
              onClick={onBack}
              disabled={isSubmitting}
              className="flex-1"
            >
              Back to Survey
            </Button>
            <Button 
              onClick={onSubmit}
              disabled={isSubmitting}
              className="flex-1 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Survey'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubmitConfirmation;
