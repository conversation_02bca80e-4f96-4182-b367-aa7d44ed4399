import axiosInstance from '@/lib/axios';
import util from '@/unmatched/utils';

const { apiUrls } = util;

interface SetPasswordPayload {
  email?: string;
  token: string;
  password: string;
  oldPassword?: string;
}

export const setPasswordFact = async (data: SetPasswordPayload) => {
  const endpoint = !data.oldPassword ? apiUrls.SET_PASSWORD : apiUrls.CHANGE_PASSWORD;
  
  return axiosInstance.post(endpoint, {
    token: data.token,
    new_password: data.password,
    ...(data.oldPassword && { old_password: data.oldPassword }),
  });
};

export const changePasswordFact = async (data: { oldPassword: string; password: string }) => {
  // Get token from session utility
  const token = util.session.getToken();
  debugger
  if (!token) {
    throw new Error('Authentication token not found');
  }

  return setPasswordFact({
    token,
    password: data.password,
    oldPassword: data.oldPassword,
  });
};
