import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'unmatched': path.resolve(__dirname, './src/unmatched'),
    }
  },
  build: {
    outDir: 'feedback',
    assetsDir: 'feedback/assets',
  },
})
