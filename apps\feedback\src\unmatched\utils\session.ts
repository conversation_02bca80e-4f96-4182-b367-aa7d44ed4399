import storage from "./local-storage";

const getToken = () => {
  return storage.getItem("token");
};

const setToken = (token: string) => {
  return storage.setItem("token", token);
};

const getExpiry = () => {
  return storage.getItem("expiry");
};

const setExpiry = (expiry: string) => {
  return storage.setItem("expiry", expiry);
};

const login = (token: string, expiry: string) => {
  setToken(token);
  setExpiry(expiry);
};

const logout = () => {
  storage.removeItem('token');
  storage.removeItem('expiry');
  storage.removeItem('persist:meta');
};

const onUnAuthorize = () => {
  logout();
  window.location.reload();
  // window.location.replace(`/auth/login/?redirect=${window.location.pathname}`);

};

const _session = {
  getToken,
  getExpiry,
  login,
  logout,
  onUnAuthorize,
};

export default _session;
