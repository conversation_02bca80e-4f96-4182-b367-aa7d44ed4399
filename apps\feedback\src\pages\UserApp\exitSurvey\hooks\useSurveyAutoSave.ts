import { useState, useCallback } from 'react';
import { saveQuestionResponse, saveCommentResponse } from '@/services/exitSurveyService';

export const useSurveyAutoSave = () => {
  const [isSaving, setIsSaving] = useState(false);
  const [saveTimeout, setSaveTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);
  const [lastSaved, setLastSaved] = useState<Date>(new Date());

  // Auto-save functionality with debouncing
  const debouncedSave = useCallback((saveFunction: () => Promise<void>) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    setIsSaving(true);

    const timeout = setTimeout(async () => {
      try {
        await saveFunction();
        setLastSaved(new Date());
      } catch (err) {
        console.error('Error saving response:', err);
      } finally {
        setIsSaving(false);
      }
    }, 1000); // 1 second debounce

    setSaveTimeout(timeout);
  }, [saveTimeout]);

  const handleResponseUpdate = useCallback((
    responseId: string,
    questionId: string,
    value: any,
    existingResponseId?: string,
    onComplete?: () => void
  ) => {
    debouncedSave(async () => {
      await saveQuestionResponse(responseId, questionId, value, existingResponseId);
      if (onComplete) {
        onComplete();
      }
    });
  }, [debouncedSave]);

  const handleCommentUpdate = useCallback((
    responseId: string,
    questionId: string,
    comment: string,
    commentId?: string
  ) => {
    debouncedSave(async () => {
      await saveCommentResponse(responseId, questionId, comment, commentId);
    });
  }, [debouncedSave]);

  return {
    isSaving,
    lastSaved,
    handleResponseUpdate,
    handleCommentUpdate
  };
};
