import React from 'react';
import { Button } from '@repo/ui/components/button';
import { Skeleton } from '@repo/ui/components/skeleton';
import { useNavigate } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import { type ExitSurveyData, type SurveySection } from '@/services/exitSurveyService';

interface SurveySidebarProps {
  isLoading: boolean;
  surveyData: ExitSurveyData | null;
  currentSection: SurveySection | null;
  onSectionSelect: (section: SurveySection) => void;
}

const SurveySidebar: React.FC<SurveySidebarProps> = ({
  isLoading,
  surveyData,
  currentSection,
  onSectionSelect
}) => {
  const navigate = useNavigate();

  const getSectionCompletionStatus = (section: SurveySection) => {
    const totalQuestions = section.questions.length;
    const answeredQuestions = section.questions.filter(q =>
      q.response?.value !== undefined &&
      q.response?.value !== null &&
      q.response?.value !== ''
    ).length;

    return {
      completed: answeredQuestions === totalQuestions && totalQuestions > 0,
      progress: totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0,
      questionsLeft: totalQuestions - answeredQuestions
    };
  };

  const handleBack = () => {
    navigate(USER_ROUTES().dashboard.surveyList);
  };

  // Calculate overall analytics
  const analytics = surveyData ? {
    total: surveyData.questions.length,
    completed: surveyData.questions.filter(q =>
      q.response?.value !== undefined &&
      q.response?.value !== null &&
      q.response?.value !== ''
    ).length
  } : { total: 0, completed: 0 };

  if (isLoading) {
    return (
      <div className="w-80 bg-white dark:bg-gray-800 border-r dark:border-gray-700 min-h-screen p-6">
        <div className="space-y-6">
          <Skeleton className="h-8 w-32" />
          <div className="space-y-2">
            {Array(5).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-10 w-full" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!surveyData) {
    return (
      <div className="w-80 bg-white dark:bg-gray-800 border-r dark:border-gray-700 min-h-screen p-6">
        <p className="text-gray-500 dark:text-gray-400">No survey data available</p>
      </div>
    );
  }

  return (
    <div className="w-48 bg-gray-100 dark:bg-gray-800 min-h-screen p-4 border-r dark:border-gray-700 flex-shrink-0">
      <Button
        variant="ghost"
        onClick={handleBack}
        className="text-blue-600 dark:text-blue-400 hover:bg-blue-600 dark:hover:bg-blue-600 p-1"
      >
        ← Back
      </Button>
      <hr className='my-4 dark:border-gray-600' />

      {/* Survey End Date */}
      <div className="mb-6">
        <h3 className="text-sm font-normal text-gray-500 dark:text-gray-400 mb-1">Survey Ends On</h3>
        <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
          {isLoading ? (
            <div className="h-4 w-32 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          ) : (
            surveyData?.meta.endDate ?
              new Date(surveyData.meta.endDate).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              }) : 'Not set'
          )}
        </p>
      </div>

      <hr className='my-4 dark:border-gray-600' />

      {/* Survey Status - Questions and Completed */}
      <div className="mb-6 space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-base font-semibold text-gray-900 dark:text-white">{analytics.total}</span>
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">Questions</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-base font-semibold text-green-600 dark:text-green-400">{analytics.completed}</span>
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">Completed</span>
        </div>
      </div>

      <hr className='my-4 dark:border-gray-600' />

      {/* Categories */}
      <div>
        <h3 className="text-sm font-normal text-gray-500 dark:text-gray-400 mb-2">Categories</h3>
        <div className="space-y-1">
          {isLoading ? (
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="h-6 w-full bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-1"></div>
            ))
          ) : (
            surveyData?.sections.map((section) => {
              const status = getSectionCompletionStatus(section);

              return (
                <div
                  key={section.id}
                  className={`p-2 rounded cursor-pointer text-sm ${currentSection?.id === section.id
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  onClick={() => onSectionSelect(section)}
                >
                  <div className="font-medium text-gray-900 dark:text-white">{section.title}</div>
                  {status.questionsLeft > 0 && (
                    <div className="text-xs font-normal text-green-600 dark:text-green-400 mt-1">
                      {status.questionsLeft} questions left
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default SurveySidebar;
