import axiosInstance from '@/lib/axios';

export interface FAQ {
  id: number;
  label: string;
  answer: string;
}

export const fetchSurveyFAQs = async (surveyId: string): Promise<FAQ[]> => {
  try {
    const response = await axiosInstance.get(`/staff/customers/faq/?survey_index=${surveyId}`);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    throw error;
  }
};
