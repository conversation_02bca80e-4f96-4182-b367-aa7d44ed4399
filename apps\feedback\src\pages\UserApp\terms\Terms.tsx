import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/ui/components/dialog';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@repo/ui/components/accordion';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { HelpCircle } from 'lucide-react';
import { fetchSurveyFAQs, FAQ } from '@/services/faqService';

interface SurveyInfo {
  id: string;
  title: string;
  description: string;
  type: string;
  resourcetype?: string;
  deadline?: string;
  start?: string;
}

const Terms: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [surveyInfo, setSurveyInfo] = useState<SurveyInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [isFaqLoading, setIsFaqLoading] = useState(false);
  const [showFaqDialog, setShowFaqDialog] = useState(false);

  useEffect(() => {
    const fetchSurveyInfo = async () => {
      if (!id) {
        setError('Survey ID is required');
        setIsLoading(false);
        return;
      }

      try {
        // Use the same API endpoint as the legacy app
        const response = await axiosInstance.get(`/survey/index/${id}/`);
        const surveyData = response.data;

        // Check if survey is still active (similar to legacy app logic)
        const deadline = surveyData.deadline;
        if (deadline && new Date(deadline) < new Date()) {
          navigate(USER_ROUTES().dashboard.surveyList);
          return;
        }

        const surveyInfo: SurveyInfo = {
          id: surveyData.id || id,
          title: surveyData.title || 'Survey',
          description: surveyData.description || 'No description available.',
          type: surveyData.resourcetype || surveyData.type || 'Unknown',
          resourcetype: surveyData.resourcetype,
          deadline: surveyData.deadline,
          start: surveyData.start
        };

        setSurveyInfo(surveyInfo);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching survey info:', err);
        setError('Failed to load survey information');
        setIsLoading(false);
      }
    };

    fetchSurveyInfo();

    // Fetch FAQs to check if button should be shown
    if (id) {
      fetchFAQs();
    }
  }, [id, navigate]);

  const fetchFAQs = async () => {
    if (!id) return;

    setIsFaqLoading(true);
    try {
      const faqData = await fetchSurveyFAQs(id);
      setFaqs(faqData);
    } catch (err) {
      console.error('Error fetching FAQs:', err);
      // Don't show error for FAQs, just keep empty array
      setFaqs([]);
    } finally {
      setIsFaqLoading(false);
    }
  };

  const handleFaqClick = () => {
    if (faqs.length === 0) {
      fetchFAQs();
    }
    setShowFaqDialog(true);
  };

  const navigateToSurvey = () => {
    if (!surveyInfo) return;

    const routes = USER_ROUTES().dashboard;
    const surveyType = surveyInfo.resourcetype || surveyInfo.type;

    // Navigate based on survey type (similar to legacy app logic)
    switch (surveyType) {
      case 'SurveyIndex360':
      case 'SurveyIndexUpward':
        navigate(routes.upwardReview.getUrl(id!));
        break;
      case 'SurveyIndexEngagement':
        navigate(routes.getEngagmentUrl(id!));
        break;
      case 'SurveyIndexSelf':
        navigate(routes.getSelfEvaluationUrl(id!));
        break;
      case 'SurveyIndexExit':
        navigate(routes.getExitUrl(id!));
        break;
      default:
        console.warn('Unknown survey type:', surveyType);
        // Fallback to upward review for unknown types
        navigate(routes.upwardReview.getUrl(id!));
        break;
    }
  };

  const onStartSurvey = async () => {
    try {
      // Remove the survey visit API call since it doesn't exist
      navigateToSurvey();
    } catch (error) {
      console.error('Error starting survey:', error);
      // TODO: Add proper error handling/toast notification
    }
  };

  const getButtonText = () => {
    return 'Start Survey';
  };

  if (error) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center text-red-500 dark:text-red-400">
        {error}
      </div>
    );
  }

  return (
    <div className="w-full p-6 bg-white dark:bg-gray-900">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          {/* Header */}
          <div className="bg-blue-500 dark:bg-blue-600 text-white p-3 rounded-t-lg">
            <h1 className="text-xl font-semibold">
              {isLoading ? (
                <div className="h-8 bg-blue-400 dark:bg-blue-500 rounded w-3/4 animate-pulse"></div>
              ) : (
                surveyInfo?.title
              )}
            </h1>
          </div>

          {/* Body */}
          <div className="p-6 min-h-[400px]">
            <div className="prose max-w-none">
              {isLoading ? (
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-11/12 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/3 animate-pulse"></div>
                </div>
              ) : (
                <div className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {surveyInfo?.description.split('\n').map((line, index) => {
                    if (line.trim() === '') {
                      return <br key={index} />;
                    }
                    return (
                      <span key={index}>
                        {line}
                        <br />
                      </span>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-8">
              <button
                onClick={onStartSurvey}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white px-6 py-2 rounded-md font-medium transition-colors"
              >
                {getButtonText()}
              </button>

              {/* FAQ Dialog - Only show if FAQs exist */}
              {faqs.length > 0 && (
                <Dialog open={showFaqDialog} onOpenChange={setShowFaqDialog}>
                  <DialogTrigger asChild>
                    <button
                      onClick={handleFaqClick}
                      disabled={isLoading}
                      className="border border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 px-6 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
                    >
                      <HelpCircle className="h-4 w-4" />
                      FAQs
                    </button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh]">
                    <DialogHeader>
                      <DialogTitle>Frequently Asked Questions</DialogTitle>
                    </DialogHeader>
                    <ScrollArea className="max-h-[60vh]">
                      {isFaqLoading ? (
                        <div className="space-y-3">
                          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-full animate-pulse"></div>
                          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 animate-pulse"></div>
                          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-full animate-pulse"></div>
                        </div>
                      ) : faqs.length > 0 ? (
                        <Accordion type="single" collapsible className="w-full">
                          {faqs.map((faq, index) => (
                            <AccordionItem key={faq.id} value={`item-${index}`}>
                              <AccordionTrigger className="text-left">
                                {faq.label}
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                                  {faq.answer}
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          No FAQs available for this survey.
                        </div>
                      )}
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Terms;
