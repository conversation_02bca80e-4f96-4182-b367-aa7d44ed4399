name: Feedback CI/CD

on:
  push:
    branches:
      - beta
  pull_request:
    branches:
      - beta

env:
  AWS_REGION: us-east-1
  NODE_VERSION: 18

permissions:
  contents: read

jobs:
  build_feedback:
    name: Build Feedback
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build Feedback
        run: |
          cd apps/feedback
          pnpm run build

      - name: Move Assets
        run: |
          cd apps/feedback
          mv feedback/feedback/* feedback/

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: feedback-build
          path: apps/feedback/feedback/**

  deploy_feedback:
    name: Deploy Feedback
    runs-on: ubuntu-latest
    needs: build_feedback

    steps:
      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: feedback-build
          path: dist

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to S3
        run: aws s3 sync dist s3://unmatched-v3-feedback-frontend-${{ github.ref_name }} --delete
