import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/ui/components/dialog';
import ChangePassword from './ChangePassword';
import useSession from '@/unmatched/modules/session/hook';

interface ChangePasswordModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({ open, onOpenChange }) => {
  const { user } = useSession();
  const isPasswordSet = user?.isPasswordSet;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isPasswordSet ? "Change Password" : "Set Password"}
          </DialogTitle>
        </DialogHeader>
        <ChangePassword closeModal={() => onOpenChange(false)} />
      </DialogContent>
    </Dialog>
  );
};

export default ChangePasswordModal;
