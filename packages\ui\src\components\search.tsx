import { Input } from "./input";
import { cn } from "../lib/utils";
import { SearchIcon } from "lucide-react";

type Props = {
  placeholder?: string;
  value?: string;
  size?: "sm" | "default";
  onChange: (value: string) => void;
};

function Search({ placeholder, value, size, onChange }: Props) {
  return (
    <div className="relative">
      <SearchIcon className="size-4 opacity-50 absolute left-2 top-1/2 -translate-y-1/2" />
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(size === "sm" ? "h-8 text-xs pl-8" : "h-9 text-sm pl-8")}
      />
    </div>
  );
}

export default Search;
