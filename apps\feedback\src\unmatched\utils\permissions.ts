// import _ from "lodash";
// import { util } from "@unmatchedoffl/ui-core";
import * as enums from "./enums";
import _ from "lodash";

const { Modules, Roles } = enums;
// const _ = util.lib;

const permissions = {
  [Roles.ADMIN]: {
    [Modules.Administration]: {
      canAccess: true,
    },
    "user-dashboard": {
      canAccess: true,
    },
  },
  [Roles.USER]: {
    [Modules.Administration]: {
      canAccess: false,
    },
    [Modules.UserDashboard]: {
      canAccess: true,
    },
  },
};

const canAccess = (role: string, module: string, access: string) => {
  const userRole = _.get(permissions, role, {});
  if (!userRole) return false;
  const app = _.get(userRole, module);
  if (!app) return false;
  const userAccess = _.get(app, access);
  return userAccess;
};

export default canAccess;
