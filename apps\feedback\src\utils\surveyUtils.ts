export interface SurveyTypeConfig {
  displayName: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
}

export const SURVEY_TYPE_CONFIGS: Record<string, SurveyTypeConfig> = {
  SurveyIndexEngagement: {
    displayName: 'Engagement Survey',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    textColor: 'text-blue-700 dark:text-blue-300',
    borderColor: 'border-blue-200 dark:border-blue-700'
  },
  SurveyIndexUpward: {
    displayName: 'Upward Feedback',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    textColor: 'text-green-700 dark:text-green-300',
    borderColor: 'border-green-200 dark:border-green-700'
  },
  SurveyIndexSelf: {
    displayName: 'Self Evaluation',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    textColor: 'text-purple-700 dark:text-purple-300',
    borderColor: 'border-purple-200 dark:border-purple-700'
  },
  SurveyIndex360: {
    displayName: '360 Degree Feedback',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    textColor: 'text-orange-700 dark:text-orange-300',
    borderColor: 'border-orange-200 dark:border-orange-700'
  },
  SurveyIndexExit: {
    displayName: 'Exit Survey',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    textColor: 'text-red-700 dark:text-red-300',
    borderColor: 'border-red-200 dark:border-red-700'
  }
};

export const getSurveyTypeConfig = (resourcetype?: string): SurveyTypeConfig => {
  if (!resourcetype || !SURVEY_TYPE_CONFIGS[resourcetype]) {
    // Default config for unknown survey types
    return {
      displayName: 'Survey',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-700',
      borderColor: 'border-gray-200'
    };
  }
  
  return SURVEY_TYPE_CONFIGS[resourcetype];
};

export const getSurveyTypeName = (resourcetype?: string): string => {
  return getSurveyTypeConfig(resourcetype).displayName;
};
