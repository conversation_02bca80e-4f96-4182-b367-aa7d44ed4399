import React, { useState } from 'react';
import { Link, NavLink } from 'react-router';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { Button } from '@repo/ui/components/button';
import { ModeToggle } from '@repo/ui/components/mode-toggle';
import useSession from '@/unmatched/modules/session/hook';
import { useTheme } from '@repo/ui/components/theme-provider';
import { useNavigate } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import util from '@/unmatched/utils';
import ChangePasswordModal from '@/components/auth/ChangePasswordModal';

const Header: React.FC = () => {
  const { user, client } = useSession();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);

  const handleLogout = () => {
    navigate('/logout');
  };

  const handleChangePassword = () => {
    setShowChangePasswordModal(true);
  };

  // Get the appropriate logo with fallback to default Unmatched logos
  const logoSrc = theme === "dark"
    ? (client?.darkLogo || util.images.UNMATCHED_DARK_LOGO)
    : (client?.lightLogo || util.images.UNMATCHED_LOGO);

  return (
    <header className="bg-card px-5 py-2 border-b">
      <nav className="flex flex-row items-center max-w-7xl mx-auto">
        {/* Logo */}
        <Link to="/user/dashboard">
          <img
            src={logoSrc}
            alt="CLIENT_LOGO"
            className="h-[20px] w-full"
          />
        </Link>

        {/* Center Navigation */}
        <div className="flex justify-center flex-grow">
          <div className="h-full header_nav">
            <NavLink to="/user/dashboard/surveys">
              Home
            </NavLink>
            <NavLink to={USER_ROUTES().contactUs}>
              Contact Us
            </NavLink>
          </div>
        </div>

        {/* Theme Toggle and User Avatar */}
        <div className="flex items-center justify-end gap-3">
          <ModeToggle />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="rounded-full text-xs bg-primary text-primary-foreground m-0 w-8 h-8 p-0 hover:bg-primary/80 hover:text-primary-foreground"
              >
                {user.firstName && user.firstName[0]}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              style={{ width: 200 }}
              align="end"
              className="mt-2 shadow-sm"
            >
              <div className="font-bold my-1 pl-2 text-break pr-2">
                {`${user.firstName} ${user.lastName}`}
              </div>
              <div className="fs-12 pl-2 pr-2 text-break text-zinc-300 line-clamp-1 text-xs">
                {user.email}
              </div>
              <hr className="my-2" />
              <DropdownMenuItem
                onClick={handleChangePassword}
                className="cursor-pointer text-xs outline-none"
              >
                {user.isPasswordSet ? "Change Password" : "Set Password"}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer text-xs outline-none text-red-500"
                onClick={handleLogout}
              >
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </nav>

      {/* Change Password Modal */}
      <ChangePasswordModal
        open={showChangePasswordModal}
        onOpenChange={setShowChangePasswordModal}
      />
    </header>
  );
};

export default Header;
