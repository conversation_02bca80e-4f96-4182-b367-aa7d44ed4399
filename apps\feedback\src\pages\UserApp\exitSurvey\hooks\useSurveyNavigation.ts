import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { submitExitSurvey, type ExitSurveyData, type SurveySection } from '@/services/exitSurveyService';
import { USER_ROUTES } from '@/app.routes';

export const useSurveyNavigation = () => {
  const navigate = useNavigate();
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNext = useCallback((
    currentSection: SurveySection | null,
    surveyData: ExitSurveyData | null,
    setCurrentSection: (section: SurveySection) => void
  ) => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    
    if (currentIndex < surveyData.sections.length - 1) {
      // Move to next section
      setCurrentSection(surveyData.sections[currentIndex + 1]);
    } else {
      // Last section - show submit confirmation
      setConfirmSubmit(true);
    }
  }, []);

  const handleBack = useCallback((
    currentSection: SurveySection | null,
    surveyData: ExitSurveyData | null,
    setCurrentSection: (section: SurveySection) => void
  ) => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    
    if (currentIndex > 0) {
      setCurrentSection(surveyData.sections[currentIndex - 1]);
    }
  }, []);

  const handleSectionSelect = useCallback((
    section: SurveySection,
    setCurrentSection: (section: SurveySection) => void
  ) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  }, []);

  const handleSubmit = useCallback(async (surveyData: ExitSurveyData | null) => {
    if (!surveyData) return;

    try {
      setIsSubmitting(true);
      await submitExitSurvey(surveyData.responseId);
      navigate(USER_ROUTES().dashboard.surveyList);
    } catch (error) {
      console.error('Error submitting survey:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [navigate]);

  return {
    confirmSubmit,
    isSubmitting,
    setConfirmSubmit,
    handleNext,
    handleBack,
    handleSectionSelect,
    handleSubmit
  };
};
